import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/constants.dart';
import '../utils/permissions.dart';
import '../utils/validators.dart';

/// 儲存結果
class SaveResult {
  final bool success;
  final String? message;
  final String? filePath;

  const SaveResult({required this.success, this.message, this.filePath});

  factory SaveResult.success({String? message, String? filePath}) {
    return SaveResult(success: true, message: message, filePath: filePath);
  }

  factory SaveResult.failure(String message) {
    return SaveResult(success: false, message: message);
  }
}

/// 儲存與分享服務
class StorageService {
  /// 儲存圖片到相簿
  static Future<SaveResult> saveImageToGallery(
    BuildContext context,
    Uint8List imageBytes, {
    String? customName,
  }) async {
    try {
      // 檢查權限
      final hasPermission = await PermissionHelper.checkAndRequestPermission(
        context,
        permissionType: 'storage',
      );

      if (!hasPermission) {
        return SaveResult.failure('沒有儲存權限');
      }

      // 生成檔案名稱
      final fileName = _generateFileName(customName);

      // 儲存到相簿
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: fileName,
        quality: AppConstants.imageQuality,
      );

      if (result['isSuccess'] == true) {
        _showSuccessSnackBar(context, '圖片已儲存到相簿');
        return SaveResult.success(
          message: '圖片已成功儲存到相簿',
          filePath: result['filePath'],
        );
      } else {
        return SaveResult.failure('儲存失敗，請重試');
      }
    } catch (e) {
      debugPrint('儲存圖片到相簿時發生錯誤: $e');
      return SaveResult.failure('儲存時發生錯誤: ${e.toString()}');
    }
  }

  /// 分享圖片
  static Future<SaveResult> shareImage(
    BuildContext context,
    Uint8List imageBytes, {
    String? text,
    String? subject,
  }) async {
    try {
      // 建立臨時檔案
      final tempDir = await getTemporaryDirectory();
      final fileName = _generateFileName();
      final tempFile = File('${tempDir.path}/$fileName');

      // 寫入圖片資料
      await tempFile.writeAsBytes(imageBytes);

      // 分享檔案
      final xFile = XFile(tempFile.path);
      await Share.shareXFiles(
        [xFile],
        text: text ?? '使用長輩圖產生器製作的圖片',
        subject: subject ?? '分享長輩圖',
      );

      // 延遲刪除臨時檔案
      Future.delayed(const Duration(seconds: 10), () {
        try {
          if (tempFile.existsSync()) {
            tempFile.deleteSync();
          }
        } catch (e) {
          debugPrint('刪除臨時檔案時發生錯誤: $e');
        }
      });

      return SaveResult.success(message: '分享成功');
    } catch (e) {
      debugPrint('分享圖片時發生錯誤: $e');
      return SaveResult.failure('分享時發生錯誤: ${e.toString()}');
    }
  }

  /// 儲存圖片到應用程式目錄
  static Future<SaveResult> saveImageToAppDirectory(
    Uint8List imageBytes, {
    String? customName,
  }) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = _generateFileName(customName);
      final file = File('${appDir.path}/$fileName');

      await file.writeAsBytes(imageBytes);

      return SaveResult.success(message: '圖片已儲存到應用程式目錄', filePath: file.path);
    } catch (e) {
      debugPrint('儲存圖片到應用程式目錄時發生錯誤: $e');
      return SaveResult.failure('儲存時發生錯誤: ${e.toString()}');
    }
  }

  /// 從應用程式目錄載入圖片
  static Future<Uint8List?> loadImageFromAppDirectory(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final file = File('${appDir.path}/$fileName');

      if (await file.exists()) {
        return await file.readAsBytes();
      }

      return null;
    } catch (e) {
      debugPrint('從應用程式目錄載入圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 取得應用程式目錄中的圖片列表
  static Future<List<String>> getAppDirectoryImages() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final directory = Directory(appDir.path);

      if (!await directory.exists()) {
        return [];
      }

      final files = await directory.list().toList();
      final imageFiles =
          files
              .whereType<File>()
              .where((file) => Validators.isValidImageFile(file.path))
              .map((file) => file.path.split('/').last)
              .toList();

      // 按修改時間排序（最新的在前）
      imageFiles.sort((a, b) {
        final fileA = File('${appDir.path}/$a');
        final fileB = File('${appDir.path}/$b');
        return fileB.lastModifiedSync().compareTo(fileA.lastModifiedSync());
      });

      return imageFiles;
    } catch (e) {
      debugPrint('取得應用程式目錄圖片列表時發生錯誤: $e');
      return [];
    }
  }

  /// 刪除應用程式目錄中的圖片
  static Future<bool> deleteImageFromAppDirectory(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final file = File('${appDir.path}/$fileName');

      if (await file.exists()) {
        await file.delete();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('刪除應用程式目錄圖片時發生錯誤: $e');
      return false;
    }
  }

  /// 清理臨時檔案
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final directory = Directory(tempDir.path);

      if (await directory.exists()) {
        final files = await directory.list().toList();

        for (final file in files) {
          if (file is File && Validators.isValidImageFile(file.path)) {
            // 刪除超過 1 小時的臨時圖片檔案
            final lastModified = await file.lastModified();
            final now = DateTime.now();
            final difference = now.difference(lastModified);

            if (difference.inHours > 1) {
              try {
                await file.delete();
              } catch (e) {
                debugPrint('刪除臨時檔案時發生錯誤: $e');
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('清理臨時檔案時發生錯誤: $e');
    }
  }

  /// 生成檔案名稱
  static String _generateFileName([String? customName]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    if (customName != null && customName.trim().isNotEmpty) {
      final cleanedName = Validators.cleanFileName(customName);
      return '${AppConstants.imageFilePrefix}${cleanedName}_$timestamp${AppConstants.imageFileExtension}';
    }

    return '${AppConstants.imageFilePrefix}$timestamp${AppConstants.imageFileExtension}';
  }

  /// 顯示成功提示
  static void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(fontSize: 16)),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 顯示儲存選項對話框
  static Future<String?> showSaveOptionsDialog(BuildContext context) async {
    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '儲存選項',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSaveOption(
                context,
                icon: Icons.photo_library,
                title: '儲存到相簿',
                subtitle: '儲存到手機相簿',
                onTap: () => Navigator.of(context).pop('gallery'),
              ),
              const SizedBox(height: 12),
              _buildSaveOption(
                context,
                icon: Icons.share,
                title: '分享圖片',
                subtitle: '透過其他應用程式分享',
                onTap: () => Navigator.of(context).pop('share'),
              ),
              const SizedBox(height: 12),
              _buildSaveOption(
                context,
                icon: Icons.folder,
                title: '儲存到應用程式',
                subtitle: '儲存到應用程式內部',
                onTap: () => Navigator.of(context).pop('app'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(fontSize: 16)),
            ),
          ],
        );
      },
    );
  }

  /// 建立儲存選項 Widget
  static Widget _buildSaveOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
