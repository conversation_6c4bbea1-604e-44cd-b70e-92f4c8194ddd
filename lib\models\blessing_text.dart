/// 祝福語文字模型
class BlessingText {
  final String id;
  final String text;
  final String category;
  final bool isCustom;

  const BlessingText({
    required this.id,
    required this.text,
    required this.category,
    this.isCustom = false,
  });

  /// 從 JSON 建立 BlessingText 物件
  factory BlessingText.fromJson(Map<String, dynamic> json) {
    return BlessingText(
      id: json['id'] as String,
      text: json['text'] as String,
      category: json['category'] as String,
      isCustom: json['isCustom'] as bool? ?? false,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {'id': id, 'text': text, 'category': category, 'isCustom': isCustom};
  }

  /// 建立自訂祝福語
  factory BlessingText.custom(String text) {
    return BlessingText(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      text: text,
      category: '自訂',
      isCustom: true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BlessingText && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'BlessingText(id: $id, text: $text, category: $category, isCustom: $isCustom)';
  }
}

/// 祝福語分類
enum BlessingCategory {
  birthday('生日祝福'),
  holiday('節日祝福'),
  health('健康祝福'),
  family('家庭祝福'),
  general('一般祝福'),
  custom('自訂');

  const BlessingCategory(this.displayName);

  final String displayName;

  /// 從字串取得分類
  static BlessingCategory fromString(String category) {
    switch (category) {
      case '生日祝福':
        return BlessingCategory.birthday;
      case '節日祝福':
        return BlessingCategory.holiday;
      case '健康祝福':
        return BlessingCategory.health;
      case '家庭祝福':
        return BlessingCategory.family;
      case '自訂':
        return BlessingCategory.custom;
      default:
        return BlessingCategory.general;
    }
  }
}

/// 預設祝福語資料
class DefaultBlessings {
  static const List<Map<String, dynamic>> data = [
    // 生日祝福
    {'id': 'birthday_1', 'text': '生日快樂！\n身體健康，萬事如意！', 'category': '生日祝福'},
    {'id': 'birthday_2', 'text': '祝您生日快樂\n年年有今日，歲歲有今朝', 'category': '生日祝福'},
    {'id': 'birthday_3', 'text': '壽比南山\n福如東海\n生日快樂！', 'category': '生日祝福'},

    // 節日祝福
    {'id': 'holiday_1', 'text': '新年快樂！\n恭喜發財！', 'category': '節日祝福'},
    {'id': 'holiday_2', 'text': '中秋節快樂\n月圓人團圓', 'category': '節日祝福'},
    {'id': 'holiday_3', 'text': '端午節安康\n粽子香，艾草長', 'category': '節日祝福'},

    // 健康祝福
    {'id': 'health_1', 'text': '身體健康\n平安喜樂', 'category': '健康祝福'},
    {'id': 'health_2', 'text': '健康長壽\n快樂每一天', 'category': '健康祝福'},
    {'id': 'health_3', 'text': '身體健康最重要\n平安就是福', 'category': '健康祝福'},

    // 家庭祝福
    {'id': 'family_1', 'text': '家和萬事興\n闔家平安', 'category': '家庭祝福'},
    {'id': 'family_2', 'text': '家庭美滿\n幸福安康', 'category': '家庭祝福'},
    {'id': 'family_3', 'text': '天倫之樂\n其樂融融', 'category': '家庭祝福'},

    // 一般祝福
    {'id': 'general_1', 'text': '祝您\n萬事如意！', 'category': '一般祝福'},
    {'id': 'general_2', 'text': '心想事成\n好運連連', 'category': '一般祝福'},
    {'id': 'general_3', 'text': '平安喜樂\n順心如意', 'category': '一般祝福'},
  ];

  /// 取得所有預設祝福語
  static List<BlessingText> getAllBlessings() {
    return data.map((item) => BlessingText.fromJson(item)).toList();
  }

  /// 根據分類取得祝福語
  static List<BlessingText> getBlessingsByCategory(BlessingCategory category) {
    return data
        .where((item) => item['category'] == category.displayName)
        .map((item) => BlessingText.fromJson(item))
        .toList();
  }
}
