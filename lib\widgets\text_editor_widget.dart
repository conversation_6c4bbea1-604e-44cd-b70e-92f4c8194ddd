import 'package:flutter/material.dart';
import '../models/blessing_text.dart';
import '../models/text_style_config.dart';
import '../services/text_service.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';

/// 文字編輯器 Widget
class TextEditorWidget extends StatefulWidget {
  final String initialText;
  final TextStyleConfig initialStyle;
  final ValueChanged<String> onTextChanged;
  final ValueChanged<TextStyleConfig> onStyleChanged;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const TextEditorWidget({
    super.key,
    required this.initialText,
    required this.initialStyle,
    required this.onTextChanged,
    required this.onStyleChanged,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  State<TextEditorWidget> createState() => _TextEditorWidgetState();
}

class _TextEditorWidgetState extends State<TextEditorWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _textController;
  late TextStyleConfig _currentStyle;

  List<BlessingText> _blessings = [];
  List<BlessingText> _filteredBlessings = [];
  BlessingCategory _selectedCategory = BlessingCategory.general;
  bool _isLoadingBlessings = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _textController = TextEditingController(text: widget.initialText);
    _currentStyle = widget.initialStyle;

    _loadBlessings();

    // 監聽文字變化
    _textController.addListener(() {
      widget.onTextChanged(_textController.text);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    super.dispose();
  }

  /// 載入祝福語
  Future<void> _loadBlessings() async {
    setState(() {
      _isLoadingBlessings = true;
    });

    try {
      _blessings = TextService.getAllBlessings();
      _filterBlessings();
    } catch (e) {
      debugPrint('載入祝福語時發生錯誤: $e');
    } finally {
      setState(() {
        _isLoadingBlessings = false;
      });
    }
  }

  /// 篩選祝福語
  void _filterBlessings() {
    setState(() {
      _filteredBlessings = TextService.getBlessingsByCategory(
        _selectedCategory,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 標籤列
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.text_fields), text: '自訂文字'),
            Tab(icon: Icon(Icons.favorite), text: '祝福語庫'),
          ],
        ),

        // 內容區域
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildCustomTextTab(), _buildBlessingsTab()],
          ),
        ),

        // 底部按鈕
        _buildBottomButtons(),
      ],
    );
  }

  /// 建立自訂文字標籤頁
  Widget _buildCustomTextTab() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 文字輸入框
          TextField(
            controller: _textController,
            maxLines: 4,
            maxLength: 200,
            decoration: const InputDecoration(
              labelText: '輸入文字內容',
              hintText: '請輸入要顯示的文字...',
              border: OutlineInputBorder(),
            ),
            style: const TextStyle(fontSize: 16),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // 快速文字按鈕
          Text(
            '快速輸入',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          Wrap(spacing: 8, runSpacing: 8, children: _buildQuickTextButtons()),

          const SizedBox(height: AppConstants.paddingMedium),

          // 文字預覽
          _buildTextPreview(),
        ],
      ),
    );
  }

  /// 建立祝福語庫標籤頁
  Widget _buildBlessingsTab() {
    return Column(
      children: [
        // 分類選擇
        _buildCategorySelector(),

        // 祝福語列表
        Expanded(
          child:
              _isLoadingBlessings
                  ? const Center(child: CircularProgressIndicator())
                  : _buildBlessingsList(),
        ),
      ],
    );
  }

  /// 建立分類選擇器
  Widget _buildCategorySelector() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: BlessingCategory.values.length,
        itemBuilder: (context, index) {
          final category = BlessingCategory.values[index];
          final isSelected = category == _selectedCategory;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: FilterChip(
              label: Text(category.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedCategory = category;
                  });
                  _filterBlessings();
                }
              },
            ),
          );
        },
      ),
    );
  }

  /// 建立祝福語列表
  Widget _buildBlessingsList() {
    if (_filteredBlessings.isEmpty) {
      return const Center(child: Text('此分類暫無祝福語'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _filteredBlessings.length,
      itemBuilder: (context, index) {
        final blessing = _filteredBlessings[index];
        return Card(
          child: ListTile(
            title: Text(
              blessing.text,
              style: const TextStyle(fontSize: 16),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(blessing.category),
            onTap: () {
              _textController.text = blessing.text;
              widget.onTextChanged(blessing.text);

              // 記錄使用
              TextService.recordBlessingUsage(blessing.id);
            },
          ),
        );
      },
    );
  }

  /// 建立快速文字按鈕
  List<Widget> _buildQuickTextButtons() {
    final quickTexts = ['生日快樂', '身體健康', '萬事如意', '恭喜發財', '新年快樂', '平安喜樂'];

    return quickTexts.map((text) {
      return ActionChip(
        label: Text(text),
        onPressed: () {
          _textController.text = text;
          widget.onTextChanged(text);
        },
      );
    }).toList();
  }

  /// 建立文字預覽
  Widget _buildTextPreview() {
    if (_textController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '預覽',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Text(
              _textController.text,
              style: _currentStyle.toTextStyle(),
              textAlign: _currentStyle.textAlign,
            ),
          ),
        ],
      ),
    );
  }

  /// 建立底部按鈕
  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // 取消按鈕
          Expanded(
            child: OutlinedButton(
              onPressed: widget.onCancel,
              child: const Text('取消'),
            ),
          ),

          const SizedBox(width: AppConstants.paddingMedium),

          // 確定按鈕
          Expanded(
            child: ElevatedButton(
              onPressed: _canConfirm() ? widget.onConfirm : null,
              child: const Text('確定'),
            ),
          ),
        ],
      ),
    );
  }

  /// 檢查是否可以確認
  bool _canConfirm() {
    final text = _textController.text.trim();
    return text.isNotEmpty && Validators.validateText(text) == null;
  }
}
