import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import '../utils/constants.dart';
import '../utils/permissions.dart';

/// 圖片來源類型
enum ImageSourceType {
  gallery, // 相簿
  camera, // 相機
  assets, // 內建圖片
  ai, // AI 生成
}

/// 圖片來源服務
class ImageSourceService {
  static final ImagePicker _picker = ImagePicker();

  /// 從相簿選擇圖片
  static Future<Uint8List?> pickFromGallery(BuildContext context) async {
    try {
      // 檢查權限
      final hasPermission = await PermissionHelper.checkAndRequestPermission(
        context,
        permissionType: 'storage',
      );

      if (!hasPermission) {
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: AppConstants.maxImageSize,
        maxHeight: AppConstants.maxImageSize,
        imageQuality: AppConstants.imageQuality,
      );

      if (image != null) {
        return await image.readAsBytes();
      }
    } catch (e) {
      debugPrint('從相簿選擇圖片時發生錯誤: $e');
      _showErrorSnackBar(context, '選擇圖片失敗，請重試');
    }

    return null;
  }

  /// 使用相機拍攝圖片
  static Future<Uint8List?> takePhoto(BuildContext context) async {
    try {
      // 檢查權限
      final hasPermission = await PermissionHelper.checkAndRequestPermission(
        context,
        permissionType: 'camera',
      );

      if (!hasPermission) {
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: AppConstants.maxImageSize,
        maxHeight: AppConstants.maxImageSize,
        imageQuality: AppConstants.imageQuality,
      );

      if (image != null) {
        return await image.readAsBytes();
      }
    } catch (e) {
      debugPrint('拍攝照片時發生錯誤: $e');
      _showErrorSnackBar(context, '拍攝照片失敗，請重試');
    }

    return null;
  }

  /// 從內建資源載入圖片
  static Future<Uint8List?> loadAssetImage(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      return data.buffer.asUint8List();
    } catch (e) {
      debugPrint('載入內建圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 使用 AI 生成圖片
  static Future<Uint8List?> generateAiImage(
    BuildContext context,
    String prompt,
  ) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // 構建 API URL
      final encodedPrompt = Uri.encodeComponent(prompt);
      final url = '${AppConstants.aiImageApiUrl}$encodedPrompt';

      // 發送 HTTP 請求
      final response = await http
          .get(
            Uri.parse(url),
            headers: {'User-Agent': 'ElderMemeGenerator/1.0'},
          )
          .timeout(AppConstants.apiTimeout);

      // 關閉載入指示器
      Navigator.of(context).pop();

      if (response.statusCode == 200) {
        final imageBytes = response.bodyBytes;

        // 驗證圖片格式
        if (_isValidImageData(imageBytes)) {
          return imageBytes;
        } else {
          _showErrorSnackBar(context, '生成的圖片格式無效');
          return null;
        }
      } else {
        _showErrorSnackBar(context, 'AI 圖片生成失敗，請重試');
        return null;
      }
    } catch (e) {
      // 關閉載入指示器
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      debugPrint('AI 圖片生成時發生錯誤: $e');
      _showErrorSnackBar(context, '網路連線失敗，請檢查網路設定');
      return null;
    }
  }

  /// 取得內建圖片列表
  static List<String> getBuiltInImages() {
    return [
      'assets/images/template_1.jpg',
      'assets/images/template_2.jpg',
      'assets/images/template_3.jpg',
      'assets/images/template_4.jpg',
      'assets/images/template_5.jpg',
      'assets/images/background_1.jpg',
      'assets/images/background_2.jpg',
      'assets/images/background_3.jpg',
      'assets/images/nature_1.jpg',
      'assets/images/nature_2.jpg',
    ];
  }

  /// 壓縮圖片
  static Future<Uint8List?> compressImage(
    Uint8List imageBytes, {
    int maxWidth = 1920,
    int maxHeight = 1920,
    int quality = 85,
  }) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return null;

      // 計算新的尺寸
      int newWidth = image.width;
      int newHeight = image.height;

      if (newWidth > maxWidth || newHeight > maxHeight) {
        final aspectRatio = newWidth / newHeight;

        if (aspectRatio > 1) {
          // 寬度較大
          newWidth = maxWidth;
          newHeight = (maxWidth / aspectRatio).round();
        } else {
          // 高度較大
          newHeight = maxHeight;
          newWidth = (maxHeight * aspectRatio).round();
        }
      }

      // 調整圖片大小
      final resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.linear,
      );

      // 編碼為 JPEG
      return Uint8List.fromList(img.encodeJpg(resizedImage, quality: quality));
    } catch (e) {
      debugPrint('壓縮圖片時發生錯誤: $e');
      return imageBytes; // 返回原始圖片
    }
  }

  /// 驗證圖片資料是否有效
  static bool _isValidImageData(Uint8List imageBytes) {
    try {
      final image = img.decodeImage(imageBytes);
      return image != null;
    } catch (e) {
      return false;
    }
  }

  /// 顯示錯誤提示
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontSize: 16)),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 顯示圖片來源選擇對話框
  static Future<ImageSourceType?> showImageSourceDialog(
    BuildContext context,
  ) async {
    return await showDialog<ImageSourceType>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '選擇圖片來源',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSourceOption(
                context,
                icon: Icons.photo_library,
                title: '從相簿選擇',
                subtitle: '選擇手機中的照片',
                onTap: () => Navigator.of(context).pop(ImageSourceType.gallery),
              ),
              const SizedBox(height: 12),
              _buildSourceOption(
                context,
                icon: Icons.camera_alt,
                title: '拍攝照片',
                subtitle: '使用相機拍攝新照片',
                onTap: () => Navigator.of(context).pop(ImageSourceType.camera),
              ),
              const SizedBox(height: 12),
              _buildSourceOption(
                context,
                icon: Icons.image,
                title: '內建圖片',
                subtitle: '使用應用程式內建圖片',
                onTap: () => Navigator.of(context).pop(ImageSourceType.assets),
              ),
              const SizedBox(height: 12),
              _buildSourceOption(
                context,
                icon: Icons.auto_awesome,
                title: 'AI 生成圖片',
                subtitle: '使用 AI 生成新圖片',
                onTap: () => Navigator.of(context).pop(ImageSourceType.ai),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(fontSize: 16)),
            ),
          ],
        );
      },
    );
  }

  /// 建立來源選項 Widget
  static Widget _buildSourceOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, size: 32, color: Theme.of(context).primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
