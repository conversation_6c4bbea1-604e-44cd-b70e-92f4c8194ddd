import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// 文字樣式配置模型
class TextStyleConfig {
  final double fontSize;
  final Color textColor;
  final Color backgroundColor;
  final String fontFamily;
  final FontWeight fontWeight;
  final TextAlign textAlign;
  final double strokeWidth;
  final Color strokeColor;
  final bool hasBackground;
  final bool hasStroke;
  final double backgroundOpacity;
  final double letterSpacing;
  final double lineHeight;

  const TextStyleConfig({
    this.fontSize = AppConstants.defaultTextSize,
    this.textColor = AppConstants.defaultTextColor,
    this.backgroundColor = AppConstants.defaultBackgroundColor,
    this.fontFamily = AppConstants.defaultFontFamily,
    this.fontWeight = FontWeight.bold,
    this.textAlign = TextAlign.center,
    this.strokeWidth = 2.0,
    this.strokeColor = Colors.black,
    this.hasBackground = true,
    this.hasStroke = false,
    this.backgroundOpacity = 0.7,
    this.letterSpacing = 0.0,
    this.lineHeight = 1.2,
  });

  /// 複製並修改部分屬性
  TextStyleConfig copyWith({
    double? fontSize,
    Color? textColor,
    Color? backgroundColor,
    String? fontFamily,
    FontWeight? fontWeight,
    TextAlign? textAlign,
    double? strokeWidth,
    Color? strokeColor,
    bool? hasBackground,
    bool? hasStroke,
    double? backgroundOpacity,
    double? letterSpacing,
    double? lineHeight,
  }) {
    return TextStyleConfig(
      fontSize: fontSize ?? this.fontSize,
      textColor: textColor ?? this.textColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      fontFamily: fontFamily ?? this.fontFamily,
      fontWeight: fontWeight ?? this.fontWeight,
      textAlign: textAlign ?? this.textAlign,
      strokeWidth: strokeWidth ?? this.strokeWidth,
      strokeColor: strokeColor ?? this.strokeColor,
      hasBackground: hasBackground ?? this.hasBackground,
      hasStroke: hasStroke ?? this.hasStroke,
      backgroundOpacity: backgroundOpacity ?? this.backgroundOpacity,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineHeight: lineHeight ?? this.lineHeight,
    );
  }

  /// 轉換為 Flutter TextStyle
  TextStyle toTextStyle() {
    return TextStyle(
      fontSize: fontSize,
      color: textColor,
      fontFamily: fontFamily,
      fontWeight: fontWeight,
      letterSpacing: letterSpacing,
      height: lineHeight,
      shadows:
          hasStroke
              ? [
                Shadow(
                  offset: Offset(-strokeWidth, -strokeWidth),
                  color: strokeColor,
                ),
                Shadow(
                  offset: Offset(strokeWidth, -strokeWidth),
                  color: strokeColor,
                ),
                Shadow(
                  offset: Offset(strokeWidth, strokeWidth),
                  color: strokeColor,
                ),
                Shadow(
                  offset: Offset(-strokeWidth, strokeWidth),
                  color: strokeColor,
                ),
              ]
              : null,
    );
  }

  /// 取得背景顏色（含透明度）
  Color get backgroundColorWithOpacity {
    return backgroundColor.withOpacity(backgroundOpacity);
  }

  /// 從 JSON 建立物件
  factory TextStyleConfig.fromJson(Map<String, dynamic> json) {
    return TextStyleConfig(
      fontSize:
          (json['fontSize'] as num?)?.toDouble() ??
          AppConstants.defaultTextSize,
      textColor: Color(
        json['textColor'] as int? ?? AppConstants.defaultTextColor.value,
      ),
      backgroundColor: Color(
        json['backgroundColor'] as int? ??
            AppConstants.defaultBackgroundColor.value,
      ),
      fontFamily:
          json['fontFamily'] as String? ?? AppConstants.defaultFontFamily,
      fontWeight:
          FontWeight.values[json['fontWeight'] as int? ??
              FontWeight.bold.index],
      textAlign:
          TextAlign.values[json['textAlign'] as int? ?? TextAlign.center.index],
      strokeWidth: (json['strokeWidth'] as num?)?.toDouble() ?? 2.0,
      strokeColor: Color(json['strokeColor'] as int? ?? Colors.black.value),
      hasBackground: json['hasBackground'] as bool? ?? true,
      hasStroke: json['hasStroke'] as bool? ?? false,
      backgroundOpacity: (json['backgroundOpacity'] as num?)?.toDouble() ?? 0.7,
      letterSpacing: (json['letterSpacing'] as num?)?.toDouble() ?? 0.0,
      lineHeight: (json['lineHeight'] as num?)?.toDouble() ?? 1.2,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'textColor': textColor.value,
      'backgroundColor': backgroundColor.value,
      'fontFamily': fontFamily,
      'fontWeight': fontWeight.index,
      'textAlign': textAlign.index,
      'strokeWidth': strokeWidth,
      'strokeColor': strokeColor.value,
      'hasBackground': hasBackground,
      'hasStroke': hasStroke,
      'backgroundOpacity': backgroundOpacity,
      'letterSpacing': letterSpacing,
      'lineHeight': lineHeight,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextStyleConfig &&
        other.fontSize == fontSize &&
        other.textColor == textColor &&
        other.backgroundColor == backgroundColor &&
        other.fontFamily == fontFamily &&
        other.fontWeight == fontWeight &&
        other.textAlign == textAlign &&
        other.strokeWidth == strokeWidth &&
        other.strokeColor == strokeColor &&
        other.hasBackground == hasBackground &&
        other.hasStroke == hasStroke &&
        other.backgroundOpacity == backgroundOpacity &&
        other.letterSpacing == letterSpacing &&
        other.lineHeight == lineHeight;
  }

  @override
  int get hashCode {
    return Object.hash(
      fontSize,
      textColor,
      backgroundColor,
      fontFamily,
      fontWeight,
      textAlign,
      strokeWidth,
      strokeColor,
      hasBackground,
      hasStroke,
      backgroundOpacity,
      letterSpacing,
      lineHeight,
    );
  }

  @override
  String toString() {
    return 'TextStyleConfig('
        'fontSize: $fontSize, '
        'textColor: $textColor, '
        'backgroundColor: $backgroundColor, '
        'fontFamily: $fontFamily, '
        'fontWeight: $fontWeight, '
        'textAlign: $textAlign, '
        'hasBackground: $hasBackground, '
        'hasStroke: $hasStroke'
        ')';
  }
}

/// 預設文字樣式配置
class DefaultTextStyles {
  /// 大標題樣式
  static const TextStyleConfig headline = TextStyleConfig(
    fontSize: 36,
    textColor: Colors.white,
    backgroundColor: Colors.black54,
    fontWeight: FontWeight.bold,
    hasBackground: true,
    hasStroke: true,
    strokeWidth: 3,
    strokeColor: Colors.black,
  );

  /// 副標題樣式
  static const TextStyleConfig subtitle = TextStyleConfig(
    fontSize: 28,
    textColor: Colors.white,
    backgroundColor: Colors.black45,
    fontWeight: FontWeight.w600,
    hasBackground: true,
  );

  /// 內文樣式
  static const TextStyleConfig body = TextStyleConfig(
    fontSize: 24,
    textColor: Colors.white,
    backgroundColor: Colors.black54,
    fontWeight: FontWeight.normal,
    hasBackground: true,
  );

  /// 小字樣式
  static const TextStyleConfig caption = TextStyleConfig(
    fontSize: 18,
    textColor: Colors.white,
    backgroundColor: Colors.black45,
    fontWeight: FontWeight.normal,
    hasBackground: true,
  );
}
