# 長輩圖產生器 (Elder Meme Generator)

一個專為年長者設計的簡易圖片編輯應用程式，讓使用者能夠輕鬆製作帶有祝福語的圖片。

## 功能特色

### 🎯 核心功能
- **圖片來源多樣化**：支援相簿選擇、相機拍攝、內建圖片、AI 圖片生成
- **文字編輯**：豐富的文字樣式設定，包含字體大小、顏色、對齊方式等
- **拖拽操作**：直觀的文字拖拽定位功能
- **祝福語庫**：內建多種分類的祝福語，快速選用
- **儲存分享**：支援儲存到相簿、應用程式目錄或直接分享

### 🎨 設計特點
- **長輩友善**：大字體、大按鈕、簡潔介面
- **無障礙設計**：符合 WCAG 標準，最小觸控目標 48x48dp
- **中文優化**：完整繁體中文介面，支援中文字體
- **響應式佈局**：適配不同螢幕尺寸

## 技術架構

### 🛠 技術棧
- **Flutter 3.7.x** - 跨平台 UI 框架
- **Dart 2.x** - 程式語言
- **Material Design 3** - 設計系統

### 📦 主要依賴
```yaml
dependencies:
  flutter:
    sdk: flutter
  image_picker: ^1.0.4          # 圖片選擇
  permission_handler: ^11.0.1   # 權限管理
  image_gallery_saver: ^2.0.3   # 圖片儲存
  share_plus: ^7.2.1            # 分享功能
  http: ^1.1.0                  # 網路請求
  google_fonts: ^6.1.0          # 字體支援
  provider: ^6.1.1              # 狀態管理
  image: ^4.1.3                 # 圖片處理
```

### 🏗 專案結構
```
lib/
├── main.dart                  # 應用程式入口
├── models/                    # 資料模型
│   ├── blessing_text.dart     # 祝福語模型
│   └── text_style_config.dart # 文字樣式配置
├── screens/                   # 畫面
│   ├── home_screen.dart       # 主畫面
│   └── image_editor_screen.dart # 圖片編輯畫面
├── widgets/                   # 自訂元件
│   ├── draggable_text_widget.dart # 可拖拽文字
│   ├── text_editor_widget.dart    # 文字編輯器
│   └── style_panel_widget.dart    # 樣式面板
├── services/                  # 服務層
│   ├── image_source_service.dart  # 圖片來源服務
│   ├── text_service.dart          # 文字服務
│   ├── image_composer.dart        # 圖片合成服務
│   └── storage_service.dart       # 儲存服務
└── utils/                     # 工具類
    ├── constants.dart         # 常數定義
    ├── permissions.dart       # 權限處理
    └── validators.dart        # 驗證工具
```

## 使用說明

### 📱 基本操作流程
1. **選擇圖片**：點擊「開始製作長輩圖」選擇圖片來源
2. **添加文字**：點擊浮動按鈕添加文字內容
3. **調整樣式**：使用底部面板調整文字樣式
4. **拖拽定位**：直接拖拽文字到想要的位置
5. **儲存分享**：點擊儲存按鈕選擇輸出方式

### 🎨 文字編輯功能
- **自訂文字**：手動輸入文字內容
- **祝福語庫**：從預設分類中選擇祝福語
- **樣式設定**：字體大小、顏色、對齊、背景等
- **特效功能**：文字描邊、背景透明度調整

### 📸 圖片來源選項
- **相簿選擇**：從裝置相簿中選擇現有圖片
- **相機拍攝**：即時拍攝新照片
- **內建圖片**：使用應用程式提供的範本圖片
- **AI 生成**：透過 AI 服務生成圖片（需網路連線）

## 開發指南

### 🚀 快速開始
```bash
# 複製專案
git clone <repository-url>
cd everyday_safety

# 安裝依賴
flutter pub get

# 執行應用程式
flutter run
```

### 🧪 測試
```bash
# 執行所有測試
flutter test

# 執行程式碼格式化
dart format .

# 執行靜態分析
flutter analyze
```

### 📋 開發規範
- **程式碼風格**：遵循 Dart 官方風格指南
- **命名規範**：變數使用 camelCase，類別使用 PascalCase
- **註解要求**：所有公開 API 需要 DartDoc 註解
- **測試覆蓋**：目標測試覆蓋率 80% 以上

### 🔧 建置配置
- **Android**：最低支援 API 21 (Android 5.0)
- **iOS**：最低支援 iOS 11.0
- **權限配置**：已設定相機、儲存、網路等必要權限

## 權限說明

### Android 權限
- `CAMERA` - 相機拍攝功能
- `READ_EXTERNAL_STORAGE` - 讀取相簿圖片
- `WRITE_EXTERNAL_STORAGE` - 儲存圖片（Android 10 以下）
- `READ_MEDIA_IMAGES` - 讀取媒體圖片（Android 13+）
- `INTERNET` - AI 圖片生成網路請求

### iOS 權限
- `NSCameraUsageDescription` - 相機使用說明
- `NSPhotoLibraryUsageDescription` - 相簿存取說明
- `NSPhotoLibraryAddUsageDescription` - 相簿儲存說明

## 版本資訊

- **當前版本**：1.0.0
- **Flutter 版本**：3.7.x
- **Dart 版本**：2.x
- **最後更新**：2024-12-30

## 授權條款

本專案採用 MIT 授權條款，詳見 [LICENSE](LICENSE) 檔案。

## 貢獻指南

歡迎提交 Issue 和 Pull Request 來改善這個專案。在貢獻之前，請確保：

1. 遵循專案的程式碼風格
2. 添加適當的測試
3. 更新相關文件
4. 確保所有測試通過

## 聯絡資訊

如有任何問題或建議，請透過以下方式聯絡：

- 提交 GitHub Issue
- 發送電子郵件至專案維護者

---

**長輩圖產生器** - 讓科技更貼近長輩的生活 ❤️
