import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../models/text_style_config.dart';
import '../services/image_composer.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';
import '../widgets/draggable_text_widget.dart';
import '../widgets/text_editor_widget.dart';
import '../widgets/style_panel_widget.dart';

/// 圖片編輯畫面
class ImageEditorScreen extends StatefulWidget {
  final Uint8List imageBytes;

  const ImageEditorScreen({super.key, required this.imageBytes});

  @override
  State<ImageEditorScreen> createState() => _ImageEditorScreenState();
}

class _ImageEditorScreenState extends State<ImageEditorScreen> {
  final GlobalKey _imageKey = GlobalKey();
  final List<TextPosition> _textPositions = [];

  String _currentText = '';
  TextStyleConfig _currentStyle = const TextStyleConfig();
  bool _isEditing = false;
  bool _isProcessing = false;
  int? _selectedTextIndex;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('編輯長輩圖'),
        actions: [
          // 儲存按鈕
          IconButton(
            onPressed: _isProcessing ? null : _saveImage,
            icon:
                _isProcessing
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.save),
            tooltip: '儲存圖片',
          ),
        ],
      ),
      body: Column(
        children: [
          // 圖片編輯區域
          Expanded(flex: 3, child: _buildImageEditArea()),

          // 控制面板
          Expanded(flex: 2, child: _buildControlPanel()),
        ],
      ),

      // 浮動按鈕 - 添加文字
      floatingActionButton: FloatingActionButton(
        onPressed: _isEditing ? null : _showTextEditor,
        child: const Icon(Icons.text_fields),
        tooltip: '添加文字',
      ),
    );
  }

  /// 建立圖片編輯區域
  Widget _buildImageEditArea() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: RepaintBoundary(
          key: _imageKey,
          child: Stack(
            children: [
              // 背景圖片
              Positioned.fill(
                child: Image.memory(widget.imageBytes, fit: BoxFit.cover),
              ),

              // 文字層
              ..._buildTextLayers(),
            ],
          ),
        ),
      ),
    );
  }

  /// 建立文字層
  List<Widget> _buildTextLayers() {
    return _textPositions.asMap().entries.map((entry) {
      final index = entry.key;
      final textPos = entry.value;

      return DraggableTextWidget(
        key: ValueKey('text_$index'),
        text: textPos.text,
        styleConfig: textPos.styleConfig,
        initialPosition: textPos.offset,
        isSelected: _selectedTextIndex == index,
        onPositionChanged: (newPosition) {
          setState(() {
            _textPositions[index] = textPos.copyWith(offset: newPosition);
          });
        },
        onTap: () {
          setState(() {
            _selectedTextIndex = _selectedTextIndex == index ? null : index;
          });
        },
        onLongPress: () {
          _editText(index);
        },
      );
    }).toList();
  }

  /// 建立控制面板
  Widget _buildControlPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // 工具列
          _buildToolbar(),

          // 樣式面板
          Expanded(
            child:
                _isEditing
                    ? TextEditorWidget(
                      initialText: _currentText,
                      initialStyle: _currentStyle,
                      onTextChanged: (text) {
                        setState(() {
                          _currentText = text;
                        });
                      },
                      onStyleChanged: (style) {
                        setState(() {
                          _currentStyle = style;
                        });
                      },
                      onConfirm: _confirmTextEdit,
                      onCancel: _cancelTextEdit,
                    )
                    : StylePanelWidget(
                      styleConfig:
                          _selectedTextIndex != null
                              ? _textPositions[_selectedTextIndex!].styleConfig
                              : _currentStyle,
                      onStyleChanged: (style) {
                        if (_selectedTextIndex != null) {
                          setState(() {
                            final textPos = _textPositions[_selectedTextIndex!];
                            _textPositions[_selectedTextIndex!] = textPos
                                .copyWith(styleConfig: style);
                          });
                        } else {
                          setState(() {
                            _currentStyle = style;
                          });
                        }
                      },
                    ),
          ),
        ],
      ),
    );
  }

  /// 建立工具列
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Row(
        children: [
          // 文字數量顯示
          Text(
            '文字: ${_textPositions.length}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          const Spacer(),

          // 刪除選中的文字
          if (_selectedTextIndex != null)
            IconButton(
              onPressed: _deleteSelectedText,
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: '刪除文字',
            ),

          // 清除所有文字
          if (_textPositions.isNotEmpty)
            IconButton(
              onPressed: _clearAllText,
              icon: const Icon(Icons.clear_all),
              tooltip: '清除所有文字',
            ),
        ],
      ),
    );
  }

  /// 顯示文字編輯器
  void _showTextEditor() {
    setState(() {
      _isEditing = true;
      _currentText = '';
      _currentStyle = const TextStyleConfig();
      _selectedTextIndex = null;
    });
  }

  /// 編輯現有文字
  void _editText(int index) {
    final textPos = _textPositions[index];
    setState(() {
      _isEditing = true;
      _currentText = textPos.text;
      _currentStyle = textPos.styleConfig;
      _selectedTextIndex = index;
    });
  }

  /// 確認文字編輯
  void _confirmTextEdit() {
    if (_currentText.trim().isEmpty) {
      _cancelTextEdit();
      return;
    }

    setState(() {
      if (_selectedTextIndex != null) {
        // 編輯現有文字
        final textPos = _textPositions[_selectedTextIndex!];
        _textPositions[_selectedTextIndex!] = textPos.copyWith(
          text: _currentText,
          styleConfig: _currentStyle,
        );
      } else {
        // 添加新文字
        _textPositions.add(
          TextPosition(
            offset: const Offset(50, 50), // 預設位置
            text: _currentText,
            styleConfig: _currentStyle,
          ),
        );
      }

      _isEditing = false;
      _selectedTextIndex = null;
    });
  }

  /// 取消文字編輯
  void _cancelTextEdit() {
    setState(() {
      _isEditing = false;
      _selectedTextIndex = null;
    });
  }

  /// 刪除選中的文字
  void _deleteSelectedText() {
    if (_selectedTextIndex != null) {
      setState(() {
        _textPositions.removeAt(_selectedTextIndex!);
        _selectedTextIndex = null;
      });
    }
  }

  /// 清除所有文字
  void _clearAllText() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('確認清除'),
            content: const Text('確定要清除所有文字嗎？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _textPositions.clear();
                    _selectedTextIndex = null;
                  });
                },
                child: const Text('確定'),
              ),
            ],
          ),
    );
  }

  /// 儲存圖片
  Future<void> _saveImage() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // 顯示儲存選項對話框
      final saveOption = await StorageService.showSaveOptionsDialog(context);

      if (saveOption != null) {
        // 合成圖片
        final composedImage = await ImageComposer.composeImage(
          imageBytes: widget.imageBytes,
          textPositions: _textPositions,
        );

        if (composedImage != null) {
          SaveResult result;

          switch (saveOption) {
            case 'gallery':
              result = await StorageService.saveImageToGallery(
                context,
                composedImage,
              );
              break;
            case 'share':
              result = await StorageService.shareImage(context, composedImage);
              break;
            case 'app':
              result = await StorageService.saveImageToAppDirectory(
                composedImage,
              );
              break;
            default:
              result = SaveResult.failure('未知的儲存選項');
          }

          if (result.success) {
            _showSuccessSnackBar(result.message ?? '操作成功');
          } else {
            _showErrorSnackBar(result.message ?? '操作失敗');
          }
        } else {
          _showErrorSnackBar('圖片合成失敗');
        }
      }
    } catch (e) {
      debugPrint('儲存圖片時發生錯誤: $e');
      _showErrorSnackBar('儲存時發生錯誤');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 顯示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  /// 顯示錯誤提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
