import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'constants.dart';

/// 權限管理工具類
class PermissionHelper {
  /// 檢查並請求儲存權限
  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      // Android 13+ 使用新的權限模型
      if (await _getAndroidVersion() >= 33) {
        final status = await Permission.photos.request();
        return status.isGranted;
      } else {
        // Android 12 及以下版本
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    return false;
  }

  /// 檢查並請求相機權限
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// 檢查儲存權限狀態
  static Future<bool> hasStoragePermission() async {
    if (Platform.isAndroid) {
      if (await _getAndroidVersion() >= 33) {
        return await Permission.photos.isGranted;
      } else {
        return await Permission.storage.isGranted;
      }
    } else if (Platform.isIOS) {
      return await Permission.photos.isGranted;
    }
    return false;
  }

  /// 檢查相機權限狀態
  static Future<bool> hasCameraPermission() async {
    return await Permission.camera.isGranted;
  }

  /// 顯示權限說明對話框
  static Future<bool> showPermissionDialog(
    BuildContext context, {
    required String title,
    required String message,
    required VoidCallback onSettings,
  }) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: Text(message, style: const TextStyle(fontSize: 16)),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消', style: TextStyle(fontSize: 16)),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onSettings();
                  },
                  child: const Text('前往設定', style: TextStyle(fontSize: 16)),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// 顯示儲存權限說明
  static Future<void> showStoragePermissionDialog(BuildContext context) async {
    await showPermissionDialog(
      context,
      title: '需要儲存權限',
      message: AppConstants.storagePermissionMessage,
      onSettings: () => openAppSettings(),
    );
  }

  /// 顯示相機權限說明
  static Future<void> showCameraPermissionDialog(BuildContext context) async {
    await showPermissionDialog(
      context,
      title: '需要相機權限',
      message: AppConstants.cameraPermissionMessage,
      onSettings: () => openAppSettings(),
    );
  }

  /// 檢查並請求所有必要權限
  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};

    // 儲存權限
    results['storage'] = await requestStoragePermission();

    // 相機權限
    results['camera'] = await requestCameraPermission();

    return results;
  }

  /// 檢查所有權限狀態
  static Future<Map<String, bool>> checkAllPermissions() async {
    final results = <String, bool>{};

    results['storage'] = await hasStoragePermission();
    results['camera'] = await hasCameraPermission();

    return results;
  }

  /// 取得 Android 版本號
  static Future<int> _getAndroidVersion() async {
    if (!Platform.isAndroid) return 0;

    try {
      // 這裡可以使用 device_info_plus 套件來取得更準確的版本資訊
      // 暫時使用簡單的方法
      return 33; // 假設為 Android 13+
    } catch (e) {
      return 30; // 預設為較舊版本
    }
  }

  /// 顯示權限被拒絕的提示
  static void showPermissionDeniedSnackBar(
    BuildContext context,
    String message,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontSize: 16)),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: '設定',
          textColor: Colors.white,
          onPressed: () => openAppSettings(),
        ),
      ),
    );
  }

  /// 檢查權限並顯示適當的提示
  static Future<bool> checkAndRequestPermission(
    BuildContext context, {
    required String permissionType,
  }) async {
    bool hasPermission = false;

    switch (permissionType) {
      case 'storage':
        hasPermission = await hasStoragePermission();
        if (!hasPermission) {
          hasPermission = await requestStoragePermission();
          if (!hasPermission) {
            showPermissionDeniedSnackBar(
              context,
              AppConstants.storagePermissionMessage,
            );
          }
        }
        break;

      case 'camera':
        hasPermission = await hasCameraPermission();
        if (!hasPermission) {
          hasPermission = await requestCameraPermission();
          if (!hasPermission) {
            showPermissionDeniedSnackBar(
              context,
              AppConstants.cameraPermissionMessage,
            );
          }
        }
        break;
    }

    return hasPermission;
  }
}
