import 'package:flutter/material.dart';

/// 應用程式常數定義
class AppConstants {
  // 應用程式資訊
  static const String appName = '長輩圖產生器';
  static const String appVersion = '1.0.0';

  // 設計規範
  static const double minTouchTarget = 48.0;
  static const double minFontSize = 16.0;
  static const double titleFontSize = 24.0;
  static const double largeFontSize = 32.0;
  static const double maxLineWidth = 80.0;

  // 間距
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // 圓角
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;

  // 圖片相關
  static const double maxImageSize = 1920.0;
  static const double previewImageSize = 300.0;
  static const int imageQuality = 85;

  // 文字樣式預設值
  static const double defaultTextSize = 24.0;
  static const Color defaultTextColor = Colors.white;
  static const Color defaultBackgroundColor = Colors.black54;
  static const String defaultFontFamily = 'NotoSansTC';

  // API 相關
  static const String aiImageApiUrl = 'https://image.pollinations.ai/prompt/';
  static const Duration apiTimeout = Duration(seconds: 30);

  // 檔案相關
  static const String imageFilePrefix = 'elder_meme_';
  static const String imageFileExtension = '.jpg';

  // 權限相關訊息
  static const String permissionDeniedMessage = '需要相關權限才能使用此功能';
  static const String storagePermissionMessage = '需要儲存權限以保存圖片到相簿';
  static const String cameraPermissionMessage = '需要相機權限以拍攝照片';
}

/// 顏色主題定義
class AppColors {
  // 主要顏色
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFFBBDEFB);

  // 次要顏色
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryLight = Color(0xFFFFE0B2);

  // 功能顏色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // 中性顏色
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF212121);
  static const Color onBackground = Color(0xFF212121);

  // 文字顏色
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textDisabled = Color(0xFFBDBDBD);

  // 邊框顏色
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);

  // 陰影顏色
  static const Color shadow = Color(0x1F000000);
}

/// 文字樣式定義
class AppTextStyles {
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle headline2 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle headline3 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    fontFamily: AppConstants.defaultFontFamily,
  );

  static const TextStyle button = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    fontFamily: AppConstants.defaultFontFamily,
  );
}

/// 動畫相關常數
class AppAnimations {
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);

  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOutCubic;
}
