import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../services/image_source_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';
import '../utils/permissions.dart';
import 'image_editor_screen.dart';

/// 主畫面
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  /// 初始化應用程式
  Future<void> _initializeApp() async {
    // 清理臨時檔案
    await StorageService.cleanupTempFiles();

    // 檢查權限狀態
    await PermissionHelper.checkAllPermissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 歡迎標題
              _buildWelcomeSection(),

              const SizedBox(height: AppConstants.paddingLarge),

              // 功能說明
              _buildFeatureDescription(),

              const SizedBox(height: AppConstants.paddingLarge),

              // 主要功能按鈕
              _buildMainButtons(),

              const SizedBox(height: AppConstants.paddingLarge),

              // 底部資訊
              _buildBottomInfo(),
            ],
          ),
        ),
      ),
    );
  }

  /// 建立歡迎區塊
  Widget _buildWelcomeSection() {
    return Column(
      children: [
        Icon(Icons.image, size: 80, color: Theme.of(context).primaryColor),
        const SizedBox(height: AppConstants.paddingMedium),
        Text(
          '歡迎使用長輩圖產生器',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          '簡單三步驟，輕鬆製作專屬長輩圖',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 建立功能說明
  Widget _buildFeatureDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          children: [
            Text(
              '製作步驟',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildStepItem(1, '選擇圖片', '從相簿、相機或內建圖片中選擇'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildStepItem(2, '添加文字', '輸入祝福語並調整樣式'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildStepItem(3, '儲存分享', '儲存到相簿或分享給朋友'),
          ],
        ),
      ),
    );
  }

  /// 建立步驟項目
  Widget _buildStepItem(int step, String title, String description) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '$step',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.paddingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 建立主要功能按鈕
  Widget _buildMainButtons() {
    return Column(
      children: [
        // 開始製作按鈕
        SizedBox(
          width: double.infinity,
          height: 60,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _startCreating,
            icon:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.add_photo_alternate, size: 28),
            label: Text(
              _isLoading ? '載入中...' : '開始製作長輩圖',
              style: const TextStyle(fontSize: 20),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        const SizedBox(height: AppConstants.paddingLarge),

        // 查看作品按鈕
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: _viewGallery,
            icon: const Icon(Icons.photo_library),
            label: const Text('查看我的作品'),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Theme.of(context).primaryColor),
            ),
          ),
        ),
      ],
    );
  }

  /// 建立底部資訊
  Widget _buildBottomInfo() {
    return Column(
      children: [
        Text(
          '版本 ${AppConstants.appVersion}',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          '專為年長者設計的簡易圖片編輯工具',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 開始製作長輩圖
  Future<void> _startCreating() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 顯示圖片來源選擇對話框
      final sourceType = await ImageSourceService.showImageSourceDialog(
        context,
      );

      if (sourceType != null) {
        Uint8List? imageBytes;

        switch (sourceType) {
          case ImageSourceType.gallery:
            imageBytes = await ImageSourceService.pickFromGallery(context);
            break;
          case ImageSourceType.camera:
            imageBytes = await ImageSourceService.takePhoto(context);
            break;
          case ImageSourceType.assets:
            await _showBuiltInImagesDialog();
            break;
          case ImageSourceType.ai:
            await _showAiImageDialog();
            break;
        }

        if (imageBytes != null) {
          // 導航到圖片編輯畫面
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ImageEditorScreen(imageBytes: imageBytes!),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('開始製作時發生錯誤: $e');
      _showErrorSnackBar('發生錯誤，請重試');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 顯示內建圖片選擇對話框
  Future<void> _showBuiltInImagesDialog() async {
    // TODO: 實作內建圖片選擇對話框
    _showInfoSnackBar('內建圖片功能開發中');
  }

  /// 顯示 AI 圖片生成對話框
  Future<void> _showAiImageDialog() async {
    // TODO: 實作 AI 圖片生成對話框
    _showInfoSnackBar('AI 圖片生成功能開發中');
  }

  /// 查看作品集
  Future<void> _viewGallery() async {
    // TODO: 實作作品集功能
    _showInfoSnackBar('作品集功能開發中');
  }

  /// 顯示錯誤提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  /// 顯示資訊提示
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.blue),
    );
  }
}
