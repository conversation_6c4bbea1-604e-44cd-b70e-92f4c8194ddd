import 'package:flutter/material.dart';
import '../models/text_style_config.dart';
import '../utils/constants.dart';

/// 樣式面板 Widget
class StylePanelWidget extends StatefulWidget {
  final TextStyleConfig styleConfig;
  final ValueChanged<TextStyleConfig> onStyleChanged;

  const StylePanelWidget({
    super.key,
    required this.styleConfig,
    required this.onStyleChanged,
  });

  @override
  State<StylePanelWidget> createState() => _StylePanelWidgetState();
}

class _StylePanelWidgetState extends State<StylePanelWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 標籤列
        TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.format_size), text: '字體'),
            Tab(icon: Icon(Icons.color_lens), text: '顏色'),
            Tab(icon: Icon(Icons.tune), text: '效果'),
          ],
        ),

        // 內容區域
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildFontTab(), _buildColorTab(), _buildEffectTab()],
          ),
        ),
      ],
    );
  }

  /// 建立字體標籤頁
  Widget _buildFontTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 字體大小
          _buildSectionTitle('字體大小'),
          _buildFontSizeSlider(),

          const SizedBox(height: AppConstants.paddingLarge),

          // 字體粗細
          _buildSectionTitle('字體粗細'),
          _buildFontWeightSelector(),

          const SizedBox(height: AppConstants.paddingLarge),

          // 文字對齊
          _buildSectionTitle('文字對齊'),
          _buildTextAlignSelector(),

          const SizedBox(height: AppConstants.paddingLarge),

          // 行高
          _buildSectionTitle('行高'),
          _buildLineHeightSlider(),
        ],
      ),
    );
  }

  /// 建立顏色標籤頁
  Widget _buildColorTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 文字顏色
          _buildSectionTitle('文字顏色'),
          _buildColorPicker(
            currentColor: widget.styleConfig.textColor,
            onColorChanged: (color) {
              _updateStyle(widget.styleConfig.copyWith(textColor: color));
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 背景顏色
          _buildSectionTitle('背景顏色'),
          _buildColorPicker(
            currentColor: widget.styleConfig.backgroundColor,
            onColorChanged: (color) {
              _updateStyle(widget.styleConfig.copyWith(backgroundColor: color));
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 背景透明度
          _buildSectionTitle('背景透明度'),
          _buildOpacitySlider(),
        ],
      ),
    );
  }

  /// 建立效果標籤頁
  Widget _buildEffectTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 背景開關
          _buildSwitchTile(
            title: '顯示背景',
            value: widget.styleConfig.hasBackground,
            onChanged: (value) {
              _updateStyle(widget.styleConfig.copyWith(hasBackground: value));
            },
          ),

          // 描邊開關
          _buildSwitchTile(
            title: '文字描邊',
            value: widget.styleConfig.hasStroke,
            onChanged: (value) {
              _updateStyle(widget.styleConfig.copyWith(hasStroke: value));
            },
          ),

          // 描邊寬度（僅在開啟描邊時顯示）
          if (widget.styleConfig.hasStroke) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            _buildSectionTitle('描邊寬度'),
            _buildStrokeWidthSlider(),
          ],

          const SizedBox(height: AppConstants.paddingLarge),

          // 預設樣式
          _buildSectionTitle('預設樣式'),
          _buildPresetStyles(),
        ],
      ),
    );
  }

  /// 建立區塊標題
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }

  /// 建立字體大小滑桿
  Widget _buildFontSizeSlider() {
    return Column(
      children: [
        Slider(
          value: widget.styleConfig.fontSize,
          min: 12,
          max: 72,
          divisions: 60,
          label: '${widget.styleConfig.fontSize.round()}',
          onChanged: (value) {
            _updateStyle(widget.styleConfig.copyWith(fontSize: value));
          },
        ),
        Text('${widget.styleConfig.fontSize.round()} px'),
      ],
    );
  }

  /// 建立字體粗細選擇器
  Widget _buildFontWeightSelector() {
    final weights = [
      FontWeight.normal,
      FontWeight.w500,
      FontWeight.w600,
      FontWeight.bold,
      FontWeight.w800,
      FontWeight.w900,
    ];

    return Wrap(
      spacing: 8,
      children:
          weights.map((weight) {
            final isSelected = widget.styleConfig.fontWeight == weight;
            return FilterChip(
              label: Text(_getFontWeightName(weight)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _updateStyle(widget.styleConfig.copyWith(fontWeight: weight));
                }
              },
            );
          }).toList(),
    );
  }

  /// 建立文字對齊選擇器
  Widget _buildTextAlignSelector() {
    final alignments = [TextAlign.left, TextAlign.center, TextAlign.right];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children:
          alignments.map((align) {
            final isSelected = widget.styleConfig.textAlign == align;
            return IconButton(
              onPressed: () {
                _updateStyle(widget.styleConfig.copyWith(textAlign: align));
              },
              icon: Icon(_getAlignIcon(align)),
              color: isSelected ? Theme.of(context).primaryColor : null,
              style: IconButton.styleFrom(
                backgroundColor:
                    isSelected
                        ? Theme.of(context).primaryColor.withOpacity(0.1)
                        : null,
              ),
            );
          }).toList(),
    );
  }

  /// 建立行高滑桿
  Widget _buildLineHeightSlider() {
    return Column(
      children: [
        Slider(
          value: widget.styleConfig.lineHeight,
          min: 0.8,
          max: 2.0,
          divisions: 24,
          label: widget.styleConfig.lineHeight.toStringAsFixed(1),
          onChanged: (value) {
            _updateStyle(widget.styleConfig.copyWith(lineHeight: value));
          },
        ),
        Text(widget.styleConfig.lineHeight.toStringAsFixed(1)),
      ],
    );
  }

  /// 建立顏色選擇器
  Widget _buildColorPicker({
    required Color currentColor,
    required ValueChanged<Color> onColorChanged,
  }) {
    final colors = [
      Colors.white,
      Colors.black,
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.yellow,
      Colors.orange,
      Colors.purple,
      Colors.pink,
      Colors.cyan,
      Colors.brown,
      Colors.grey,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          colors.map((color) {
            final isSelected = currentColor.value == color.value;
            return GestureDetector(
              onTap: () => onColorChanged(color),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child:
                    isSelected
                        ? const Icon(Icons.check, color: Colors.white)
                        : null,
              ),
            );
          }).toList(),
    );
  }

  /// 建立透明度滑桿
  Widget _buildOpacitySlider() {
    return Column(
      children: [
        Slider(
          value: widget.styleConfig.backgroundOpacity,
          min: 0.0,
          max: 1.0,
          divisions: 20,
          label: '${(widget.styleConfig.backgroundOpacity * 100).round()}%',
          onChanged: (value) {
            _updateStyle(widget.styleConfig.copyWith(backgroundOpacity: value));
          },
        ),
        Text('${(widget.styleConfig.backgroundOpacity * 100).round()}%'),
      ],
    );
  }

  /// 建立描邊寬度滑桿
  Widget _buildStrokeWidthSlider() {
    return Column(
      children: [
        Slider(
          value: widget.styleConfig.strokeWidth,
          min: 1.0,
          max: 8.0,
          divisions: 7,
          label: '${widget.styleConfig.strokeWidth.round()}',
          onChanged: (value) {
            _updateStyle(widget.styleConfig.copyWith(strokeWidth: value));
          },
        ),
        Text('${widget.styleConfig.strokeWidth.round()} px'),
      ],
    );
  }

  /// 建立開關項目
  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 建立預設樣式
  Widget _buildPresetStyles() {
    final presets = [
      ('標題', DefaultTextStyles.headline),
      ('副標題', DefaultTextStyles.subtitle),
      ('內文', DefaultTextStyles.body),
      ('小字', DefaultTextStyles.caption),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          presets.map((preset) {
            return ActionChip(
              label: Text(preset.$1),
              onPressed: () {
                _updateStyle(preset.$2);
              },
            );
          }).toList(),
    );
  }

  /// 更新樣式
  void _updateStyle(TextStyleConfig newStyle) {
    widget.onStyleChanged(newStyle);
  }

  /// 取得字體粗細名稱
  String _getFontWeightName(FontWeight weight) {
    switch (weight) {
      case FontWeight.normal:
        return '正常';
      case FontWeight.w500:
        return '中等';
      case FontWeight.w600:
        return '半粗';
      case FontWeight.bold:
        return '粗體';
      case FontWeight.w800:
        return '特粗';
      case FontWeight.w900:
        return '極粗';
      default:
        return '正常';
    }
  }

  /// 取得對齊圖示
  IconData _getAlignIcon(TextAlign align) {
    switch (align) {
      case TextAlign.left:
        return Icons.format_align_left;
      case TextAlign.center:
        return Icons.format_align_center;
      case TextAlign.right:
        return Icons.format_align_right;
      default:
        return Icons.format_align_center;
    }
  }
}
