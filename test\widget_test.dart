// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:everyday_safety/main.dart';

void main() {
  testWidgets('Elder Meme App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ElderMemeApp());

    // Verify that the app title is displayed.
    expect(find.text('長輩圖產生器'), findsOneWidget);

    // Verify that the welcome message is displayed.
    expect(find.text('歡迎使用長輩圖產生器'), findsOneWidget);

    // Verify that the start button is displayed.
    expect(find.text('開始製作長輩圖'), findsOneWidget);
  });
}
