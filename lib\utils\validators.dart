/// 輸入驗證工具類
class Validators {
  /// 驗證文字內容
  static String? validateText(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入文字內容';
    }

    if (value.trim().length > 200) {
      return '文字內容不能超過 200 個字元';
    }

    return null;
  }

  /// 驗證字體大小
  static String? validateFontSize(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入字體大小';
    }

    final fontSize = double.tryParse(value);
    if (fontSize == null) {
      return '請輸入有效的數字';
    }

    if (fontSize < 12) {
      return '字體大小不能小於 12';
    }

    if (fontSize > 100) {
      return '字體大小不能大於 100';
    }

    return null;
  }

  /// 驗證透明度值
  static String? validateOpacity(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入透明度值';
    }

    final opacity = double.tryParse(value);
    if (opacity == null) {
      return '請輸入有效的數字';
    }

    if (opacity < 0 || opacity > 1) {
      return '透明度值必須在 0 到 1 之間';
    }

    return null;
  }

  /// 驗證描邊寬度
  static String? validateStrokeWidth(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入描邊寬度';
    }

    final strokeWidth = double.tryParse(value);
    if (strokeWidth == null) {
      return '請輸入有效的數字';
    }

    if (strokeWidth < 0) {
      return '描邊寬度不能小於 0';
    }

    if (strokeWidth > 10) {
      return '描邊寬度不能大於 10';
    }

    return null;
  }

  /// 驗證字元間距
  static String? validateLetterSpacing(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // 字元間距可以為空
    }

    final letterSpacing = double.tryParse(value);
    if (letterSpacing == null) {
      return '請輸入有效的數字';
    }

    if (letterSpacing < -5 || letterSpacing > 10) {
      return '字元間距必須在 -5 到 10 之間';
    }

    return null;
  }

  /// 驗證行高
  static String? validateLineHeight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // 行高可以為空
    }

    final lineHeight = double.tryParse(value);
    if (lineHeight == null) {
      return '請輸入有效的數字';
    }

    if (lineHeight < 0.5 || lineHeight > 3.0) {
      return '行高必須在 0.5 到 3.0 之間';
    }

    return null;
  }

  /// 驗證 AI 圖片提示詞
  static String? validateAiPrompt(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入圖片描述';
    }

    if (value.trim().length < 3) {
      return '圖片描述至少需要 3 個字元';
    }

    if (value.trim().length > 500) {
      return '圖片描述不能超過 500 個字元';
    }

    // 檢查是否包含不適當的內容
    final inappropriateWords = [
      '暴力',
      '血腥',
      '色情',
      '政治',
      '宗教',
      'violence',
      'blood',
      'sexual',
      'political',
      'religious',
    ];

    final lowerValue = value.toLowerCase();
    for (final word in inappropriateWords) {
      if (lowerValue.contains(word)) {
        return '圖片描述包含不適當的內容';
      }
    }

    return null;
  }

  /// 驗證檔案名稱
  static String? validateFileName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入檔案名稱';
    }

    // 檢查檔案名稱是否包含非法字元
    final invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    for (final char in invalidChars) {
      if (value.contains(char)) {
        return '檔案名稱不能包含特殊字元: $char';
      }
    }

    if (value.length > 100) {
      return '檔案名稱不能超過 100 個字元';
    }

    return null;
  }

  /// 清理文字內容
  static String cleanText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// 清理檔案名稱
  static String cleanFileName(String fileName) {
    // 移除非法字元
    final invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    String cleaned = fileName;

    for (final char in invalidChars) {
      cleaned = cleaned.replaceAll(char, '_');
    }

    // 移除多餘的空格和特殊字元
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), '_');

    // 限制長度
    if (cleaned.length > 100) {
      cleaned = cleaned.substring(0, 100);
    }

    return cleaned;
  }

  /// 驗證顏色值（十六進位）
  static String? validateHexColor(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '請輸入顏色值';
    }

    final hexPattern = RegExp(r'^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$');
    if (!hexPattern.hasMatch(value)) {
      return '請輸入有效的十六進位顏色值 (例如: #FF0000)';
    }

    return null;
  }

  /// 檢查是否為有效的圖片檔案
  static bool isValidImageFile(String fileName) {
    final validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    final lowerFileName = fileName.toLowerCase();

    return validExtensions.any((ext) => lowerFileName.endsWith(ext));
  }

  /// 檢查文字是否包含表情符號
  static bool containsEmoji(String text) {
    final emojiPattern = RegExp(
      r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]',
      unicode: true,
    );

    return emojiPattern.hasMatch(text);
  }
}
