import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/blessing_text.dart';
import '../models/text_style_config.dart';
import '../utils/validators.dart';

/// 文字服務類別
class TextService {
  static List<BlessingText> _blessings = [];
  static List<BlessingText> _customBlessings = [];

  /// 初始化文字服務
  static Future<void> initialize() async {
    await _loadDefaultBlessings();
    await _loadCustomBlessings();
  }

  /// 載入預設祝福語
  static Future<void> _loadDefaultBlessings() async {
    try {
      _blessings = DefaultBlessings.getAllBlessings();
    } catch (e) {
      debugPrint('載入預設祝福語時發生錯誤: $e');
      _blessings = DefaultBlessings.getAllBlessings();
    }
  }

  /// 載入自訂祝福語
  static Future<void> _loadCustomBlessings() async {
    try {
      // 這裡可以從本地儲存載入自訂祝福語
      // 暫時使用空列表
      _customBlessings = [];
    } catch (e) {
      debugPrint('載入自訂祝福語時發生錯誤: $e');
      _customBlessings = [];
    }
  }

  /// 取得所有祝福語
  static List<BlessingText> getAllBlessings() {
    return [..._blessings, ..._customBlessings];
  }

  /// 根據分類取得祝福語
  static List<BlessingText> getBlessingsByCategory(BlessingCategory category) {
    if (category == BlessingCategory.custom) {
      return _customBlessings;
    }

    return _blessings
        .where((blessing) => blessing.category == category.displayName)
        .toList();
  }

  /// 搜尋祝福語
  static List<BlessingText> searchBlessings(String query) {
    if (query.trim().isEmpty) {
      return getAllBlessings();
    }

    final lowerQuery = query.toLowerCase();
    return getAllBlessings()
        .where(
          (blessing) =>
              blessing.text.toLowerCase().contains(lowerQuery) ||
              blessing.category.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// 新增自訂祝福語
  static Future<bool> addCustomBlessing(String text) async {
    try {
      final validationResult = Validators.validateText(text);
      if (validationResult != null) {
        return false;
      }

      final cleanedText = Validators.cleanText(text);
      final customBlessing = BlessingText.custom(cleanedText);

      // 檢查是否已存在相同的祝福語
      final exists = _customBlessings.any(
        (blessing) => blessing.text == cleanedText,
      );

      if (!exists) {
        _customBlessings.add(customBlessing);
        await _saveCustomBlessings();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('新增自訂祝福語時發生錯誤: $e');
      return false;
    }
  }

  /// 刪除自訂祝福語
  static Future<bool> removeCustomBlessing(String id) async {
    try {
      final index = _customBlessings.indexWhere(
        (blessing) => blessing.id == id,
      );

      if (index != -1) {
        _customBlessings.removeAt(index);
        await _saveCustomBlessings();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('刪除自訂祝福語時發生錯誤: $e');
      return false;
    }
  }

  /// 儲存自訂祝福語
  static Future<void> _saveCustomBlessings() async {
    try {
      // 這裡可以實作本地儲存邏輯
      // 例如使用 shared_preferences 或 sqflite
      debugPrint('儲存自訂祝福語: ${_customBlessings.length} 筆');
    } catch (e) {
      debugPrint('儲存自訂祝福語時發生錯誤: $e');
    }
  }

  /// 格式化文字以適合顯示
  static String formatTextForDisplay(String text, {int maxLength = 50}) {
    if (text.length <= maxLength) {
      return text;
    }

    return '${text.substring(0, maxLength)}...';
  }

  /// 計算文字行數
  static int calculateTextLines(String text) {
    return text.split('\n').length;
  }

  /// 計算文字寬度（估算）
  static double estimateTextWidth(String text, TextStyleConfig styleConfig) {
    // 這是一個簡單的估算方法
    // 實際應用中可能需要使用 TextPainter 來精確計算
    final averageCharWidth = styleConfig.fontSize * 0.6;
    final maxLineLength = text
        .split('\n')
        .map((line) => line.length)
        .reduce((a, b) => a > b ? a : b);

    return maxLineLength * averageCharWidth;
  }

  /// 計算文字高度（估算）
  static double estimateTextHeight(String text, TextStyleConfig styleConfig) {
    final lines = calculateTextLines(text);
    return lines * styleConfig.fontSize * styleConfig.lineHeight;
  }

  /// 驗證文字內容
  static String? validateTextContent(String? text) {
    return Validators.validateText(text);
  }

  /// 清理文字內容
  static String cleanTextContent(String text) {
    return Validators.cleanText(text);
  }

  /// 檢查文字是否包含表情符號
  static bool containsEmoji(String text) {
    return Validators.containsEmoji(text);
  }

  /// 取得建議的字體大小
  static double getSuggestedFontSize(String text, double containerWidth) {
    final textLength = text.replaceAll('\n', '').length;

    if (textLength <= 10) {
      return 36.0;
    } else if (textLength <= 20) {
      return 32.0;
    } else if (textLength <= 40) {
      return 28.0;
    } else {
      return 24.0;
    }
  }

  /// 取得建議的文字顏色（基於背景亮度）
  static Color getSuggestedTextColor(Color backgroundColor) {
    // 計算背景顏色的亮度
    final brightness = backgroundColor.computeLuminance();

    // 如果背景較亮，使用深色文字；如果背景較暗，使用淺色文字
    return brightness > 0.5 ? Colors.black : Colors.white;
  }

  /// 取得對比色
  static Color getContrastColor(Color color) {
    final brightness = color.computeLuminance();
    return brightness > 0.5 ? Colors.black : Colors.white;
  }

  /// 分割長文字為多行
  static String splitLongText(String text, {int maxCharsPerLine = 15}) {
    if (text.length <= maxCharsPerLine) {
      return text;
    }

    final words = text.split(' ');
    final lines = <String>[];
    String currentLine = '';

    for (final word in words) {
      if ((currentLine + word).length <= maxCharsPerLine) {
        currentLine += (currentLine.isEmpty ? '' : ' ') + word;
      } else {
        if (currentLine.isNotEmpty) {
          lines.add(currentLine);
          currentLine = word;
        } else {
          // 單字太長，強制分割
          lines.add(word);
        }
      }
    }

    if (currentLine.isNotEmpty) {
      lines.add(currentLine);
    }

    return lines.join('\n');
  }

  /// 取得熱門祝福語
  static List<BlessingText> getPopularBlessings({int limit = 10}) {
    // 這裡可以根據使用頻率或其他指標來排序
    // 暫時返回前幾個預設祝福語
    final allBlessings = getAllBlessings();
    return allBlessings.take(limit).toList();
  }

  /// 取得最近使用的祝福語
  static List<BlessingText> getRecentBlessings({int limit = 5}) {
    // 這裡可以從本地儲存載入最近使用的祝福語
    // 暫時返回空列表
    return [];
  }

  /// 記錄祝福語使用
  static Future<void> recordBlessingUsage(String blessingId) async {
    try {
      // 這裡可以實作使用記錄邏輯
      debugPrint('記錄祝福語使用: $blessingId');
    } catch (e) {
      debugPrint('記錄祝福語使用時發生錯誤: $e');
    }
  }
}
