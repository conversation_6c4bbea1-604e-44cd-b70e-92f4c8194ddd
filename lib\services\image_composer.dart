import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image/image.dart' as img;
import '../models/text_style_config.dart';

/// 文字位置資訊
class TextPosition {
  final Offset offset;
  final String text;
  final TextStyleConfig styleConfig;

  const TextPosition({
    required this.offset,
    required this.text,
    required this.styleConfig,
  });

  TextPosition copyWith({
    Offset? offset,
    String? text,
    TextStyleConfig? styleConfig,
  }) {
    return TextPosition(
      offset: offset ?? this.offset,
      text: text ?? this.text,
      styleConfig: styleConfig ?? this.styleConfig,
    );
  }
}

/// 圖片合成服務
class ImageComposer {
  /// 合成圖片和文字
  static Future<Uint8List?> composeImage({
    required Uint8List imageBytes,
    required List<TextPosition> textPositions,
    double? outputWidth,
    double? outputHeight,
    int quality = 90,
  }) async {
    try {
      // 解碼原始圖片
      final originalImage = img.decodeImage(imageBytes);
      if (originalImage == null) {
        debugPrint('無法解碼圖片');
        return null;
      }

      // 設定輸出尺寸
      final targetWidth = outputWidth?.toInt() ?? originalImage.width;
      final targetHeight = outputHeight?.toInt() ?? originalImage.height;

      // 調整圖片大小
      final resizedImage = img.copyResize(
        originalImage,
        width: targetWidth,
        height: targetHeight,
        interpolation: img.Interpolation.linear,
      );

      // 建立畫布
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // 繪製背景圖片
      final imageData = Uint8List.fromList(img.encodePng(resizedImage));
      final codec = await ui.instantiateImageCodec(imageData);
      final frame = await codec.getNextFrame();
      final backgroundImage = frame.image;

      canvas.drawImage(backgroundImage, Offset.zero, Paint());

      // 繪製文字
      for (final textPos in textPositions) {
        await _drawText(
          canvas,
          textPos.text,
          textPos.offset,
          textPos.styleConfig,
          Size(targetWidth.toDouble(), targetHeight.toDouble()),
        );
      }

      // 完成繪製
      final picture = recorder.endRecording();
      final finalImage = await picture.toImage(targetWidth, targetHeight);
      final byteData = await finalImage.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        final pngBytes = byteData.buffer.asUint8List();

        // 如果需要 JPEG 格式，進行轉換
        if (quality < 100) {
          final decodedImage = img.decodePng(pngBytes);
          if (decodedImage != null) {
            return Uint8List.fromList(
              img.encodeJpg(decodedImage, quality: quality),
            );
          }
        }

        return pngBytes;
      }

      return null;
    } catch (e) {
      debugPrint('合成圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 繪製文字到畫布
  static Future<void> _drawText(
    Canvas canvas,
    String text,
    Offset position,
    TextStyleConfig styleConfig,
    Size canvasSize,
  ) async {
    try {
      // 建立文字樣式
      final textStyle = styleConfig.toTextStyle();

      // 建立文字段落
      final paragraphBuilder = ui.ParagraphBuilder(
        ui.ParagraphStyle(
          textAlign: styleConfig.textAlign,
          fontSize: styleConfig.fontSize,
          fontFamily: styleConfig.fontFamily,
          fontWeight: styleConfig.fontWeight,
          height: styleConfig.lineHeight,
        ),
      );

      // 如果有描邊效果
      if (styleConfig.hasStroke) {
        paragraphBuilder.pushStyle(
          ui.TextStyle(
            color: styleConfig.strokeColor,
            fontSize: styleConfig.fontSize,
            fontFamily: styleConfig.fontFamily,
            fontWeight: styleConfig.fontWeight,
            letterSpacing: styleConfig.letterSpacing,
            shadows: _createStrokeShadows(
              styleConfig.strokeColor,
              styleConfig.strokeWidth,
            ),
          ),
        );
        paragraphBuilder.addText(text);
        paragraphBuilder.pop();
      }

      // 主要文字
      paragraphBuilder.pushStyle(
        ui.TextStyle(
          color: styleConfig.textColor,
          fontSize: styleConfig.fontSize,
          fontFamily: styleConfig.fontFamily,
          fontWeight: styleConfig.fontWeight,
          letterSpacing: styleConfig.letterSpacing,
        ),
      );
      paragraphBuilder.addText(text);
      paragraphBuilder.pop();

      // 建立段落
      final paragraph = paragraphBuilder.build();
      paragraph.layout(ui.ParagraphConstraints(width: canvasSize.width * 0.8));

      // 計算文字位置
      final textWidth = paragraph.maxIntrinsicWidth;
      final textHeight = paragraph.height;

      double x = position.dx;
      double y = position.dy;

      // 根據對齊方式調整位置
      switch (styleConfig.textAlign) {
        case TextAlign.center:
          x = position.dx - (textWidth / 2);
          break;
        case TextAlign.right:
          x = position.dx - textWidth;
          break;
        case TextAlign.left:
        default:
          // 保持原始位置
          break;
      }

      // 確保文字不會超出畫布邊界
      x = x.clamp(0, canvasSize.width - textWidth);
      y = y.clamp(0, canvasSize.height - textHeight);

      final textPosition = Offset(x, y);

      // 繪製背景
      if (styleConfig.hasBackground) {
        final backgroundRect = Rect.fromLTWH(
          x - 8,
          y - 4,
          textWidth + 16,
          textHeight + 8,
        );

        final backgroundPaint =
            Paint()
              ..color = styleConfig.backgroundColorWithOpacity
              ..style = PaintingStyle.fill;

        canvas.drawRRect(
          RRect.fromRectAndRadius(backgroundRect, const Radius.circular(4)),
          backgroundPaint,
        );
      }

      // 繪製文字
      canvas.drawParagraph(paragraph, textPosition);
    } catch (e) {
      debugPrint('繪製文字時發生錯誤: $e');
    }
  }

  /// 建立描邊陰影效果
  static List<ui.Shadow> _createStrokeShadows(Color strokeColor, double width) {
    final shadows = <ui.Shadow>[];

    // 建立多個方向的陰影來模擬描邊效果
    for (double x = -width; x <= width; x += width / 2) {
      for (double y = -width; y <= width; y += width / 2) {
        if (x != 0 || y != 0) {
          shadows.add(
            ui.Shadow(offset: Offset(x, y), color: strokeColor, blurRadius: 0),
          );
        }
      }
    }

    return shadows;
  }

  /// 從 Widget 生成圖片
  static Future<Uint8List?> captureWidget(
    Widget widget, {
    double pixelRatio = 3.0,
    Size? size,
  }) async {
    try {
      final RenderRepaintBoundary repaintBoundary = RenderRepaintBoundary();

      final RenderView renderView = RenderView(
        child: RenderPositionedBox(
          alignment: Alignment.center,
          child: repaintBoundary,
        ),
        configuration: ViewConfiguration(
          logicalConstraints: BoxConstraints.tight(
            size ?? const Size(400, 400),
          ),
          devicePixelRatio: pixelRatio,
        ),
        view: WidgetsBinding.instance.platformDispatcher.views.first,
      );

      final PipelineOwner pipelineOwner = PipelineOwner();
      final BuildOwner buildOwner = BuildOwner(focusManager: FocusManager());

      pipelineOwner.rootNode = renderView;
      renderView.prepareInitialFrame();

      final RenderObjectToWidgetElement<RenderBox> rootElement =
          RenderObjectToWidgetAdapter<RenderBox>(
            container: repaintBoundary,
            child: widget,
          ).attachToRenderTree(buildOwner);

      buildOwner.buildScope(rootElement);
      buildOwner.finalizeTree();

      pipelineOwner.flushLayout();
      pipelineOwner.flushCompositingBits();
      pipelineOwner.flushPaint();

      final ui.Image image = await repaintBoundary.toImage(
        pixelRatio: pixelRatio,
      );
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      return byteData?.buffer.asUint8List();
    } catch (e) {
      debugPrint('從 Widget 生成圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 計算文字的邊界框
  static Rect calculateTextBounds(
    String text,
    TextStyleConfig styleConfig,
    Size canvasSize,
  ) {
    try {
      final textPainter = TextPainter(
        text: TextSpan(text: text, style: styleConfig.toTextStyle()),
        textDirection: TextDirection.ltr,
        textAlign: styleConfig.textAlign,
      );

      textPainter.layout(maxWidth: canvasSize.width * 0.8);

      return Rect.fromLTWH(0, 0, textPainter.width, textPainter.height);
    } catch (e) {
      debugPrint('計算文字邊界時發生錯誤: $e');
      return Rect.zero;
    }
  }

  /// 檢查文字是否在畫布範圍內
  static bool isTextInBounds(
    Offset position,
    String text,
    TextStyleConfig styleConfig,
    Size canvasSize,
  ) {
    final textBounds = calculateTextBounds(text, styleConfig, canvasSize);
    final textRect = Rect.fromLTWH(
      position.dx,
      position.dy,
      textBounds.width,
      textBounds.height,
    );

    final canvasRect = Rect.fromLTWH(0, 0, canvasSize.width, canvasSize.height);
    return canvasRect.contains(textRect.topLeft) &&
        canvasRect.contains(textRect.bottomRight);
  }
}
