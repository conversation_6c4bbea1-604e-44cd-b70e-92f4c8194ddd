import 'package:flutter/material.dart';
import '../models/text_style_config.dart';
import '../utils/constants.dart';

/// 可拖拽的文字 Widget
class DraggableTextWidget extends StatefulWidget {
  final String text;
  final TextStyleConfig styleConfig;
  final Offset initialPosition;
  final bool isSelected;
  final ValueChanged<Offset> onPositionChanged;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const DraggableTextWidget({
    super.key,
    required this.text,
    required this.styleConfig,
    required this.initialPosition,
    this.isSelected = false,
    required this.onPositionChanged,
    this.onTap,
    this.onLongPress,
  });

  @override
  State<DraggableTextWidget> createState() => _DraggableTextWidgetState();
}

class _DraggableTextWidgetState extends State<DraggableTextWidget>
    with SingleTickerProviderStateMixin {
  late Offset _position;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;

    // 初始化動畫控制器
    _animationController = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: AppAnimations.defaultCurve,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(DraggableTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果選中狀態改變，播放動畫
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  border:
                      widget.isSelected
                          ? Border.all(color: Colors.blue, width: 2)
                          : null,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _buildTextWidget(),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 建立文字 Widget
  Widget _buildTextWidget() {
    return Container(
      padding:
          widget.styleConfig.hasBackground
              ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
              : EdgeInsets.zero,
      decoration:
          widget.styleConfig.hasBackground
              ? BoxDecoration(
                color: widget.styleConfig.backgroundColorWithOpacity,
                borderRadius: BorderRadius.circular(4),
              )
              : null,
      child: Text(
        widget.text,
        style: widget.styleConfig.toTextStyle(),
        textAlign: widget.styleConfig.textAlign,
      ),
    );
  }

  /// 開始拖拽
  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });

    // 觸發選中狀態
    widget.onTap?.call();

    // 播放縮放動畫
    _animationController.forward();
  }

  /// 拖拽更新
  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _position += details.delta;
    });

    // 即時回調位置變化
    widget.onPositionChanged(_position);
  }

  /// 結束拖拽
  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });

    // 如果沒有被選中，恢復原始大小
    if (!widget.isSelected) {
      _animationController.reverse();
    }

    // 確保文字在邊界內
    _constrainPosition();
  }

  /// 限制位置在邊界內
  void _constrainPosition() {
    // 獲取父容器的大小
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final parentSize = renderBox.size;
    final textSize = _calculateTextSize();

    // 計算邊界
    final minX = 0.0;
    final minY = 0.0;
    final maxX = parentSize.width - textSize.width;
    final maxY = parentSize.height - textSize.height;

    // 限制位置
    final constrainedX = _position.dx.clamp(minX, maxX);
    final constrainedY = _position.dy.clamp(minY, maxY);
    final constrainedPosition = Offset(constrainedX, constrainedY);

    // 如果位置有變化，更新位置
    if (constrainedPosition != _position) {
      setState(() {
        _position = constrainedPosition;
      });
      widget.onPositionChanged(_position);
    }
  }

  /// 計算文字大小
  Size _calculateTextSize() {
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.text,
        style: widget.styleConfig.toTextStyle(),
      ),
      textDirection: TextDirection.ltr,
      textAlign: widget.styleConfig.textAlign,
    );

    textPainter.layout();

    // 加上背景的 padding
    final padding =
        widget.styleConfig.hasBackground
            ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
            : EdgeInsets.zero;

    return Size(
      textPainter.width + padding.horizontal,
      textPainter.height + padding.vertical,
    );
  }
}

/// 文字拖拽指示器
class TextDragIndicator extends StatelessWidget {
  final bool isVisible;
  final Offset position;

  const TextDragIndicator({
    super.key,
    required this.isVisible,
    required this.position,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      left: position.dx - 20,
      top: position.dy - 20,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.3),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.blue, width: 2),
        ),
        child: const Icon(Icons.drag_indicator, color: Colors.blue, size: 20),
      ),
    );
  }
}

/// 文字邊界指示器
class TextBoundaryIndicator extends StatelessWidget {
  final Rect bounds;
  final bool isVisible;

  const TextBoundaryIndicator({
    super.key,
    required this.bounds,
    required this.isVisible,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      left: bounds.left,
      top: bounds.top,
      width: bounds.width,
      height: bounds.height,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.red,
            width: 1,
            style: BorderStyle.solid,
          ),
        ),
      ),
    );
  }
}

/// 文字對齊輔助線
class TextAlignmentGuide extends StatelessWidget {
  final List<Offset> horizontalLines;
  final List<Offset> verticalLines;
  final bool isVisible;

  const TextAlignmentGuide({
    super.key,
    required this.horizontalLines,
    required this.verticalLines,
    required this.isVisible,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return CustomPaint(
      painter: _AlignmentGuidePainter(
        horizontalLines: horizontalLines,
        verticalLines: verticalLines,
      ),
      size: Size.infinite,
    );
  }
}

/// 對齊輔助線繪製器
class _AlignmentGuidePainter extends CustomPainter {
  final List<Offset> horizontalLines;
  final List<Offset> verticalLines;

  _AlignmentGuidePainter({
    required this.horizontalLines,
    required this.verticalLines,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.orange.withOpacity(0.7)
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

    // 繪製水平輔助線
    for (final line in horizontalLines) {
      canvas.drawLine(Offset(0, line.dy), Offset(size.width, line.dy), paint);
    }

    // 繪製垂直輔助線
    for (final line in verticalLines) {
      canvas.drawLine(Offset(line.dx, 0), Offset(line.dx, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
